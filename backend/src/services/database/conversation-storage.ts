// Conversation Storage Service for MiCA Therapy Application
// Handles all database operations for conversations, messages, and AI analysis

import { supabase } from '../../config/database.js';
import { dataValidationService } from './validation.js';
import {
  DatabaseConversation,
  DatabaseMessage,
  DatabaseAIAnalysis,
  DatabaseTherapeuticApproach,
  DatabaseSessionAnalytics,
  DatabaseMultiTherapistSession,
  ConversationStorageData,
  MessageStorageData,
  AIAnalysisStorageData,
  TherapeuticApproachStorageData,
  SessionAnalyticsStorageData,
  MultiTherapistSessionStorageData,
  ConversationQueryOptions,
  MessageQueryOptions,
  AnalysisQueryOptions,
  PatientAnalysis,
  TherapeuticApproachInfo,
  ModeSpecificAnalysis
} from '../../types/index.js';

export class ConversationStorageService {
  
  // ==================== CONVERSATION OPERATIONS ====================
  
  /**
   * Create a new conversation record
   */
  async createConversation(data: ConversationStorageData): Promise<DatabaseConversation> {
    try {
      // Validate input data
      const validation = dataValidationService.validateConversationData(data);
      if (!validation.isValid) {
        throw new Error(`Invalid conversation data: ${validation.errors.join(', ')}`);
      }

      const conversationData = {
        id: data.id,
        status: data.status,
        config: data.config,
        session_metadata: data.sessionMetadata,
        patient_persona_id: data.patientPersonaId,
        therapist_mode: data.therapistMode,
        session_type: data.sessionType
      };

      const { data: conversation, error } = await supabase
        .from('conversations')
        .insert(conversationData)
        .select()
        .single();

      if (error) {
        console.error('❌ Error creating conversation:', error);
        throw new Error(`Failed to create conversation: ${error.message}`);
      }

      console.log(`✅ Created conversation: ${conversation.id}`);
      return this.mapDatabaseConversation(conversation);
    } catch (error) {
      console.error('❌ Error in createConversation:', error);
      throw error;
    }
  }

  /**
   * Update conversation status and metadata
   */
  async updateConversation(id: string, updates: Partial<ConversationStorageData>): Promise<DatabaseConversation> {
    try {
      const updateData: any = {};
      
      if (updates.status) updateData.status = updates.status;
      if (updates.config) updateData.config = updates.config;
      if (updates.sessionMetadata) updateData.session_metadata = updates.sessionMetadata;
      if (updates.patientPersonaId) updateData.patient_persona_id = updates.patientPersonaId;
      if (updates.therapistMode) updateData.therapist_mode = updates.therapistMode;
      if (updates.sessionType) updateData.session_type = updates.sessionType;

      const { data: conversation, error } = await supabase
        .from('conversations')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('❌ Error updating conversation:', error);
        throw new Error(`Failed to update conversation: ${error.message}`);
      }

      console.log(`✅ Updated conversation: ${id}`);
      return this.mapDatabaseConversation(conversation);
    } catch (error) {
      console.error('❌ Error in updateConversation:', error);
      throw error;
    }
  }

  /**
   * Get conversation by ID
   */
  async getConversation(id: string): Promise<DatabaseConversation | null> {
    try {
      const { data: conversation, error } = await supabase
        .from('conversations')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') { // No rows returned
          return null;
        }
        console.error('❌ Error fetching conversation:', error);
        throw new Error(`Failed to fetch conversation: ${error.message}`);
      }

      return this.mapDatabaseConversation(conversation);
    } catch (error) {
      console.error('❌ Error in getConversation:', error);
      throw error;
    }
  }

  /**
   * Query conversations with filters
   */
  async queryConversations(options: ConversationQueryOptions = {}): Promise<DatabaseConversation[]> {
    try {
      let query = supabase
        .from('conversations')
        .select('*');

      // Apply filters
      if (options.status) {
        query = query.eq('status', options.status);
      }
      if (options.patientPersonaId) {
        query = query.eq('patient_persona_id', options.patientPersonaId);
      }
      if (options.therapistMode) {
        query = query.eq('therapist_mode', options.therapistMode);
      }
      if (options.sessionType) {
        query = query.eq('session_type', options.sessionType);
      }
      if (options.dateRange) {
        query = query
          .gte('created_at', options.dateRange.start)
          .lte('created_at', options.dateRange.end);
      }

      // Apply ordering
      const orderBy = options.orderBy || 'created_at';
      const orderDirection = options.orderDirection || 'desc';
      query = query.order(orderBy, { ascending: orderDirection === 'asc' });

      // Apply pagination
      if (options.limit) {
        const offset = options.offset || 0;
        query = query.range(offset, offset + options.limit - 1);
      }

      const { data: conversations, error } = await query;

      if (error) {
        console.error('❌ Error querying conversations:', error);
        throw new Error(`Failed to query conversations: ${error.message}`);
      }

      return conversations.map(conv => this.mapDatabaseConversation(conv));
    } catch (error) {
      console.error('❌ Error in queryConversations:', error);
      throw error;
    }
  }

  // ==================== MESSAGE OPERATIONS ====================

  /**
   * Store a message with optional AI analysis
   */
  async storeMessage(
    messageData: MessageStorageData,
    aiAnalysis?: PatientAnalysis,
    therapeuticApproach?: TherapeuticApproachInfo
  ): Promise<DatabaseMessage> {
    try {
      // Validate input data
      const validation = dataValidationService.validateMessageData(messageData);
      if (!validation.isValid) {
        throw new Error(`Invalid message data: ${validation.errors.join(', ')}`);
      }
      // Start a transaction-like operation
      const { data: message, error: messageError } = await supabase
        .from('messages')
        .insert({
          id: messageData.id,
          conversation_id: messageData.conversationId,
          sender: messageData.sender,
          content: messageData.content,
          metadata: messageData.metadata || {},
          message_type: messageData.messageType || 'standard',
          thinking: messageData.thinking,
          processing_time: messageData.processingTime,
          confidence_score: messageData.confidenceScore
        })
        .select()
        .single();

      if (messageError) {
        console.error('❌ Error storing message:', messageError);
        throw new Error(`Failed to store message: ${messageError.message}`);
      }

      // Store AI analysis if provided
      if (aiAnalysis && messageData.sender === 'patient') {
        await this.storeAIAnalysis({
          messageId: message.id,
          conversationId: messageData.conversationId,
          analysisType: 'patient_analysis',
          analysisData: aiAnalysis
        });
      }

      // Store therapeutic approach if provided
      if (therapeuticApproach && messageData.sender === 'therapist') {
        await this.storeTherapeuticApproach({
          messageId: message.id,
          conversationId: messageData.conversationId,
          approachId: therapeuticApproach.id,
          approachName: therapeuticApproach.name,
          techniqueId: therapeuticApproach.selectedTechnique.id,
          techniqueName: therapeuticApproach.selectedTechnique.name,
          rationale: therapeuticApproach.selectedTechnique.description
        });
      }

      console.log(`✅ Stored message: ${message.id}`);
      return this.mapDatabaseMessage(message);
    } catch (error) {
      console.error('❌ Error in storeMessage:', error);
      throw error;
    }
  }

  /**
   * Get messages for a conversation
   */
  async getConversationMessages(conversationId: string, options: MessageQueryOptions = {}): Promise<DatabaseMessage[]> {
    try {
      let query = supabase
        .from('messages')
        .select('*')
        .eq('conversation_id', conversationId);

      // Apply filters
      if (options.sender) {
        query = query.eq('sender', options.sender);
      }
      if (options.messageType) {
        query = query.eq('message_type', options.messageType);
      }
      if (options.dateRange) {
        query = query
          .gte('created_at', options.dateRange.start)
          .lte('created_at', options.dateRange.end);
      }

      // Apply ordering and pagination
      query = query.order('created_at', { ascending: true });
      
      if (options.limit) {
        const offset = options.offset || 0;
        query = query.range(offset, offset + options.limit - 1);
      }

      const { data: messages, error } = await query;

      if (error) {
        console.error('❌ Error fetching messages:', error);
        throw new Error(`Failed to fetch messages: ${error.message}`);
      }

      return messages.map(msg => this.mapDatabaseMessage(msg));
    } catch (error) {
      console.error('❌ Error in getConversationMessages:', error);
      throw error;
    }
  }

  // ==================== AI ANALYSIS OPERATIONS ====================

  /**
   * Store AI analysis data
   */
  async storeAIAnalysis(data: AIAnalysisStorageData): Promise<DatabaseAIAnalysis> {
    try {
      const { data: analysis, error } = await supabase
        .from('ai_analysis')
        .insert({
          message_id: data.messageId,
          conversation_id: data.conversationId,
          analysis_type: data.analysisType,
          analysis_data: data.analysisData
        })
        .select()
        .single();

      if (error) {
        console.error('❌ Error storing AI analysis:', error);
        throw new Error(`Failed to store AI analysis: ${error.message}`);
      }

      console.log(`✅ Stored AI analysis for message: ${data.messageId}`);
      return this.mapDatabaseAIAnalysis(analysis);
    } catch (error) {
      console.error('❌ Error in storeAIAnalysis:', error);
      throw error;
    }
  }

  /**
   * Get AI analysis for a message
   */
  async getMessageAnalysis(messageId: string, analysisType?: string): Promise<DatabaseAIAnalysis[]> {
    try {
      let query = supabase
        .from('ai_analysis')
        .select('*')
        .eq('message_id', messageId);

      if (analysisType) {
        query = query.eq('analysis_type', analysisType);
      }

      const { data: analyses, error } = await query;

      if (error) {
        console.error('❌ Error fetching AI analysis:', error);
        throw new Error(`Failed to fetch AI analysis: ${error.message}`);
      }

      return analyses.map(analysis => this.mapDatabaseAIAnalysis(analysis));
    } catch (error) {
      console.error('❌ Error in getMessageAnalysis:', error);
      throw error;
    }
  }

  /**
   * Get all AI analysis for a conversation
   */
  async getConversationAnalysis(conversationId: string, options: AnalysisQueryOptions = {}): Promise<DatabaseAIAnalysis[]> {
    try {
      let query = supabase
        .from('ai_analysis')
        .select('*')
        .eq('conversation_id', conversationId);

      if (options.analysisType) {
        query = query.eq('analysis_type', options.analysisType);
      }
      if (options.dateRange) {
        query = query
          .gte('created_at', options.dateRange.start)
          .lte('created_at', options.dateRange.end);
      }

      query = query.order('created_at', { ascending: true });

      if (options.limit) {
        const offset = options.offset || 0;
        query = query.range(offset, offset + options.limit - 1);
      }

      const { data: analyses, error } = await query;

      if (error) {
        console.error('❌ Error fetching conversation analysis:', error);
        throw new Error(`Failed to fetch conversation analysis: ${error.message}`);
      }

      return analyses.map(analysis => this.mapDatabaseAIAnalysis(analysis));
    } catch (error) {
      console.error('❌ Error in getConversationAnalysis:', error);
      throw error;
    }
  }

  // ==================== THERAPEUTIC APPROACH OPERATIONS ====================

  /**
   * Store therapeutic approach data
   */
  async storeTherapeuticApproach(data: TherapeuticApproachStorageData): Promise<DatabaseTherapeuticApproach> {
    try {
      const { data: approach, error } = await supabase
        .from('therapeutic_approaches')
        .insert({
          message_id: data.messageId,
          conversation_id: data.conversationId,
          approach_id: data.approachId,
          approach_name: data.approachName,
          technique_id: data.techniqueId,
          technique_name: data.techniqueName,
          rationale: data.rationale,
          effectiveness_prediction: data.effectivenessPrediction
        })
        .select()
        .single();

      if (error) {
        console.error('❌ Error storing therapeutic approach:', error);
        throw new Error(`Failed to store therapeutic approach: ${error.message}`);
      }

      console.log(`✅ Stored therapeutic approach for message: ${data.messageId}`);
      return this.mapDatabaseTherapeuticApproach(approach);
    } catch (error) {
      console.error('❌ Error in storeTherapeuticApproach:', error);
      throw error;
    }
  }

  /**
   * Get therapeutic approaches for a conversation
   */
  async getConversationTherapeuticApproaches(conversationId: string): Promise<DatabaseTherapeuticApproach[]> {
    try {
      const { data: approaches, error } = await supabase
        .from('therapeutic_approaches')
        .select('*')
        .eq('conversation_id', conversationId)
        .order('created_at', { ascending: true });

      if (error) {
        console.error('❌ Error fetching therapeutic approaches:', error);
        throw new Error(`Failed to fetch therapeutic approaches: ${error.message}`);
      }

      return approaches.map(approach => this.mapDatabaseTherapeuticApproach(approach));
    } catch (error) {
      console.error('❌ Error in getConversationTherapeuticApproaches:', error);
      throw error;
    }
  }

  // ==================== SESSION ANALYTICS OPERATIONS ====================

  /**
   * Store or update session analytics
   */
  async storeSessionAnalytics(data: SessionAnalyticsStorageData): Promise<DatabaseSessionAnalytics> {
    try {
      const analyticsData = {
        conversation_id: data.conversationId,
        total_messages: data.totalMessages,
        patient_messages: data.patientMessages,
        therapist_messages: data.therapistMessages,
        session_duration: data.sessionDuration,
        avg_response_time: data.avgResponseTime,
        sentiment_progression: data.sentimentProgression,
        engagement_progression: data.engagementProgression,
        motivation_progression: data.motivationProgression,
        therapeutic_techniques_used: data.therapeuticTechniquesUsed,
        readiness_score_progression: data.readinessScoreProgression
      };

      // Use upsert to handle updates
      const { data: analytics, error } = await supabase
        .from('session_analytics')
        .upsert(analyticsData, { onConflict: 'conversation_id' })
        .select()
        .single();

      if (error) {
        console.error('❌ Error storing session analytics:', error);
        throw new Error(`Failed to store session analytics: ${error.message}`);
      }

      console.log(`✅ Stored session analytics for conversation: ${data.conversationId}`);
      return this.mapDatabaseSessionAnalytics(analytics);
    } catch (error) {
      console.error('❌ Error in storeSessionAnalytics:', error);
      throw error;
    }
  }

  /**
   * Get session analytics for a conversation
   */
  async getSessionAnalytics(conversationId: string): Promise<DatabaseSessionAnalytics | null> {
    try {
      const { data: analytics, error } = await supabase
        .from('session_analytics')
        .select('*')
        .eq('conversation_id', conversationId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') { // No rows returned
          return null;
        }
        console.error('❌ Error fetching session analytics:', error);
        throw new Error(`Failed to fetch session analytics: ${error.message}`);
      }

      return this.mapDatabaseSessionAnalytics(analytics);
    } catch (error) {
      console.error('❌ Error in getSessionAnalytics:', error);
      throw error;
    }
  }

  // ==================== HELPER METHODS ====================

  private mapDatabaseConversation(dbConv: any): DatabaseConversation {
    return {
      id: dbConv.id,
      status: dbConv.status,
      config: dbConv.config,
      session_metadata: dbConv.session_metadata,
      patient_persona_id: dbConv.patient_persona_id,
      therapist_mode: dbConv.therapist_mode,
      session_type: dbConv.session_type,
      created_at: dbConv.created_at,
      updated_at: dbConv.updated_at
    };
  }

  private mapDatabaseMessage(dbMsg: any): DatabaseMessage {
    return {
      id: dbMsg.id,
      conversation_id: dbMsg.conversation_id,
      sender: dbMsg.sender,
      content: dbMsg.content,
      metadata: dbMsg.metadata,
      message_type: dbMsg.message_type,
      thinking: dbMsg.thinking,
      processing_time: dbMsg.processing_time,
      confidence_score: dbMsg.confidence_score,
      created_at: dbMsg.created_at
    };
  }

  private mapDatabaseAIAnalysis(dbAnalysis: any): DatabaseAIAnalysis {
    return {
      id: dbAnalysis.id,
      message_id: dbAnalysis.message_id,
      conversation_id: dbAnalysis.conversation_id,
      analysis_type: dbAnalysis.analysis_type,
      analysis_data: dbAnalysis.analysis_data,
      created_at: dbAnalysis.created_at
    };
  }

  private mapDatabaseTherapeuticApproach(dbApproach: any): DatabaseTherapeuticApproach {
    return {
      id: dbApproach.id,
      message_id: dbApproach.message_id,
      conversation_id: dbApproach.conversation_id,
      approach_id: dbApproach.approach_id,
      approach_name: dbApproach.approach_name,
      technique_id: dbApproach.technique_id,
      technique_name: dbApproach.technique_name,
      rationale: dbApproach.rationale,
      effectiveness_prediction: dbApproach.effectiveness_prediction,
      created_at: dbApproach.created_at
    };
  }

  private mapDatabaseSessionAnalytics(dbAnalytics: any): DatabaseSessionAnalytics {
    return {
      id: dbAnalytics.id,
      conversation_id: dbAnalytics.conversation_id,
      total_messages: dbAnalytics.total_messages,
      patient_messages: dbAnalytics.patient_messages,
      therapist_messages: dbAnalytics.therapist_messages,
      session_duration: dbAnalytics.session_duration,
      avg_response_time: dbAnalytics.avg_response_time,
      sentiment_progression: dbAnalytics.sentiment_progression,
      engagement_progression: dbAnalytics.engagement_progression,
      motivation_progression: dbAnalytics.motivation_progression,
      therapeutic_techniques_used: dbAnalytics.therapeutic_techniques_used,
      readiness_score_progression: dbAnalytics.readiness_score_progression,
      created_at: dbAnalytics.created_at,
      updated_at: dbAnalytics.updated_at
    };
  }
}
