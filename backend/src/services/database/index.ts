// Database Services Index
// Unified access point for all database operations

import { ConversationStorageService } from './conversation-storage.js';
import { MultiTherapistStorageService } from './multi-therapist-storage.js';
import {
  DatabaseConversation,
  DatabaseMessage,
  DatabaseAIAnalysis,
  DatabaseTherapeuticApproach,
  DatabaseSessionAnalytics,
  DatabaseMultiTherapistSession,
  ConversationStorageData,
  MessageStorageData,
  AIAnalysisStorageData,
  TherapeuticApproachStorageData,
  SessionAnalyticsStorageData,
  MultiTherapistSessionStorageData,
  ConversationQueryOptions,
  MessageQueryOptions,
  AnalysisQueryOptions,
  PatientAnalysis,
  TherapeuticApproachInfo,
  ModeSpecificAnalysis,
  TherapistPersonaType
} from '../../types/index.js';

/**
 * Unified Database Service
 * Provides a single interface for all conversation storage operations
 */
export class DatabaseService {
  private conversationStorage: ConversationStorageService;
  private multiTherapistStorage: MultiTherapistStorageService;

  constructor() {
    this.conversationStorage = new ConversationStorageService();
    this.multiTherapistStorage = new MultiTherapistStorageService();
  }

  // ==================== CONVERSATION OPERATIONS ====================

  async createConversation(data: ConversationStorageData): Promise<DatabaseConversation> {
    return this.conversationStorage.createConversation(data);
  }

  async updateConversation(id: string, updates: Partial<ConversationStorageData>): Promise<DatabaseConversation> {
    return this.conversationStorage.updateConversation(id, updates);
  }

  async getConversation(id: string): Promise<DatabaseConversation | null> {
    return this.conversationStorage.getConversation(id);
  }

  async queryConversations(options: ConversationQueryOptions = {}): Promise<DatabaseConversation[]> {
    return this.conversationStorage.queryConversations(options);
  }

  // ==================== MESSAGE OPERATIONS ====================

  async storeMessage(
    messageData: MessageStorageData,
    aiAnalysis?: PatientAnalysis,
    therapeuticApproach?: TherapeuticApproachInfo
  ): Promise<DatabaseMessage> {
    return this.conversationStorage.storeMessage(messageData, aiAnalysis, therapeuticApproach);
  }

  async getConversationMessages(conversationId: string, options: MessageQueryOptions = {}): Promise<DatabaseMessage[]> {
    return this.conversationStorage.getConversationMessages(conversationId, options);
  }

  // ==================== AI ANALYSIS OPERATIONS ====================

  async storeAIAnalysis(data: AIAnalysisStorageData): Promise<DatabaseAIAnalysis> {
    return this.conversationStorage.storeAIAnalysis(data);
  }

  async getMessageAnalysis(messageId: string, analysisType?: string): Promise<DatabaseAIAnalysis[]> {
    return this.conversationStorage.getMessageAnalysis(messageId, analysisType);
  }

  async getConversationAnalysis(conversationId: string, options: AnalysisQueryOptions = {}): Promise<DatabaseAIAnalysis[]> {
    return this.conversationStorage.getConversationAnalysis(conversationId, options);
  }

  // ==================== THERAPEUTIC APPROACH OPERATIONS ====================

  async storeTherapeuticApproach(data: TherapeuticApproachStorageData): Promise<DatabaseTherapeuticApproach> {
    return this.conversationStorage.storeTherapeuticApproach(data);
  }

  async getConversationTherapeuticApproaches(conversationId: string): Promise<DatabaseTherapeuticApproach[]> {
    return this.conversationStorage.getConversationTherapeuticApproaches(conversationId);
  }

  // ==================== SESSION ANALYTICS OPERATIONS ====================

  async storeSessionAnalytics(data: SessionAnalyticsStorageData): Promise<DatabaseSessionAnalytics> {
    return this.conversationStorage.storeSessionAnalytics(data);
  }

  async getSessionAnalytics(conversationId: string): Promise<DatabaseSessionAnalytics | null> {
    return this.conversationStorage.getSessionAnalytics(conversationId);
  }

  // ==================== MULTI-THERAPIST OPERATIONS ====================

  async storeMultiTherapistSession(data: MultiTherapistSessionStorageData): Promise<DatabaseMultiTherapistSession> {
    return this.multiTherapistStorage.storeMultiTherapistSession(data);
  }

  async updateMultiTherapistSession(
    conversationId: string, 
    personaType: TherapistPersonaType, 
    updates: Partial<MultiTherapistSessionStorageData>
  ): Promise<DatabaseMultiTherapistSession> {
    return this.multiTherapistStorage.updateMultiTherapistSession(conversationId, personaType, updates);
  }

  async getMultiTherapistSessions(conversationId: string): Promise<DatabaseMultiTherapistSession[]> {
    return this.multiTherapistStorage.getMultiTherapistSessions(conversationId);
  }

  async getPersonaSession(conversationId: string, personaType: TherapistPersonaType): Promise<DatabaseMultiTherapistSession | null> {
    return this.multiTherapistStorage.getPersonaSession(conversationId, personaType);
  }

  async getPerformanceComparison(conversationId: string): Promise<{
    cbtOnly?: DatabaseMultiTherapistSession;
    miFixedPretreatment?: DatabaseMultiTherapistSession;
    dynamicAdaptive?: DatabaseMultiTherapistSession;
  }> {
    return this.multiTherapistStorage.getPerformanceComparison(conversationId);
  }

  async storeAllPersonaMetrics(
    conversationId: string,
    metricsData: {
      cbtOnly?: Partial<MultiTherapistSessionStorageData>;
      miFixedPretreatment?: Partial<MultiTherapistSessionStorageData>;
      dynamicAdaptive?: Partial<MultiTherapistSessionStorageData>;
    }
  ): Promise<DatabaseMultiTherapistSession[]> {
    return this.multiTherapistStorage.storeAllPersonaMetrics(conversationId, metricsData);
  }

  // ==================== COMPREHENSIVE OPERATIONS ====================

  /**
   * Get complete conversation data including messages, analysis, and session data
   */
  async getCompleteConversationData(conversationId: string): Promise<{
    conversation: DatabaseConversation | null;
    messages: DatabaseMessage[];
    analysis: DatabaseAIAnalysis[];
    therapeuticApproaches: DatabaseTherapeuticApproach[];
    sessionAnalytics: DatabaseSessionAnalytics | null;
    multiTherapistSessions?: DatabaseMultiTherapistSession[];
  }> {
    try {
      const [
        conversation,
        messages,
        analysis,
        therapeuticApproaches,
        sessionAnalytics
      ] = await Promise.all([
        this.getConversation(conversationId),
        this.getConversationMessages(conversationId),
        this.getConversationAnalysis(conversationId),
        this.getConversationTherapeuticApproaches(conversationId),
        this.getSessionAnalytics(conversationId)
      ]);

      const result: any = {
        conversation,
        messages,
        analysis,
        therapeuticApproaches,
        sessionAnalytics
      };

      // Include multi-therapist data if applicable
      if (conversation?.therapist_mode === 'multi-therapist') {
        result.multiTherapistSessions = await this.getMultiTherapistSessions(conversationId);
      }

      return result;
    } catch (error) {
      console.error('❌ Error in getCompleteConversationData:', error);
      throw error;
    }
  }

  /**
   * Store complete message with all associated data
   */
  async storeCompleteMessage(
    messageData: MessageStorageData,
    aiAnalysis?: PatientAnalysis,
    therapeuticApproach?: TherapeuticApproachInfo,
    modeSpecificAnalysis?: ModeSpecificAnalysis
  ): Promise<{
    message: DatabaseMessage;
    analysis?: DatabaseAIAnalysis[];
    therapeuticApproachRecord?: DatabaseTherapeuticApproach;
  }> {
    try {
      // Store the message first
      const message = await this.storeMessage(messageData, aiAnalysis, therapeuticApproach);

      const result: any = { message };

      // Store mode-specific analysis if provided
      if (modeSpecificAnalysis && messageData.sender === 'patient') {
        const modeAnalysis = await this.storeAIAnalysis({
          messageId: message.id,
          conversationId: messageData.conversationId,
          analysisType: 'mode_specific',
          analysisData: modeSpecificAnalysis
        });
        
        result.analysis = [modeAnalysis];
        
        // Add patient analysis if it was stored
        if (aiAnalysis) {
          const patientAnalysisRecords = await this.getMessageAnalysis(message.id, 'patient_analysis');
          result.analysis = [...result.analysis, ...patientAnalysisRecords];
        }
      }

      // Get therapeutic approach record if it was stored
      if (therapeuticApproach && messageData.sender === 'therapist') {
        const approaches = await this.getConversationTherapeuticApproaches(messageData.conversationId);
        result.therapeuticApproachRecord = approaches.find(a => a.message_id === message.id);
      }

      return result;
    } catch (error) {
      console.error('❌ Error in storeCompleteMessage:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const databaseService = new DatabaseService();

// Export individual services for direct access if needed
export { ConversationStorageService, MultiTherapistStorageService };
