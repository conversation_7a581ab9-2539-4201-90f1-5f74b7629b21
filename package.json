{"name": "mica-therapy-simulation", "version": "1.0.0", "description": "AI Therapy Simulation with dual agent conversation interface", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm run test", "test:backend": "cd backend && npm run test", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "setup": "npm run setup:frontend && npm run setup:backend", "setup:frontend": "cd frontend && npm install", "setup:backend": "cd backend && npm install", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "format": "prettier --write \"**/*.{js,ts,svelte,json,md}\"", "clean": "rm -rf frontend/dist backend/dist frontend/node_modules backend/node_modules node_modules", "migrate": "ts-node scripts/migrate-database.ts migrate", "migrate:rollback": "ts-node scripts/migrate-database.ts rollback", "migrate:status": "ts-node scripts/migrate-database.ts status"}, "keywords": ["ai", "therapy", "simulation", "chatbot", "svelte", "supabase", "openai", "typescript"], "author": "MiCA Development Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "prettier": "^3.0.3", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "eslint": "^8.50.0"}, "workspaces": ["frontend", "backend"], "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}