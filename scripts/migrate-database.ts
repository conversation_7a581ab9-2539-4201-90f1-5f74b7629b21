#!/usr/bin/env ts-node

// Database Migration Script for MiCA Therapy Application
// Applies the enhanced conversation storage schema to Supabase database

import { createClient } from '@supabase/supabase-js';
import * as fs from 'fs';
import * as path from 'path';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, '../.env') });

const supabaseUrl = process.env['SUPABASE_URL'];
const supabaseServiceKey = process.env['SUPABASE_SERVICE_KEY'];

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables: SUPABASE_URL and SUPABASE_SERVICE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

interface MigrationFile {
  filename: string;
  path: string;
  content: string;
}

class DatabaseMigrator {
  private migrationsPath: string;

  constructor() {
    this.migrationsPath = path.resolve(__dirname, '../database/migrations');
  }

  /**
   * Get all migration files in order
   */
  private getMigrationFiles(): MigrationFile[] {
    try {
      const files = fs.readdirSync(this.migrationsPath)
        .filter(file => file.endsWith('.sql'))
        .sort(); // Ensure migrations run in order

      return files.map(filename => ({
        filename,
        path: path.join(this.migrationsPath, filename),
        content: fs.readFileSync(path.join(this.migrationsPath, filename), 'utf8')
      }));
    } catch (error) {
      console.error('❌ Error reading migration files:', error);
      throw error;
    }
  }

  /**
   * Check if migrations table exists, create if not
   */
  private async ensureMigrationsTable(): Promise<void> {
    try {
      // First, try to query the migrations table to see if it exists
      const { error: queryError } = await supabase
        .from('schema_migrations')
        .select('count')
        .limit(1);

      if (queryError && queryError.code === 'PGRST116') {
        // Table doesn't exist, we need to create it manually
        console.log('⚠️  Migrations table does not exist. Please create it manually in your Supabase dashboard:');
        console.log(`
CREATE TABLE IF NOT EXISTS schema_migrations (
  id SERIAL PRIMARY KEY,
  filename VARCHAR(255) NOT NULL UNIQUE,
  applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  checksum VARCHAR(64)
);
        `);
        throw new Error('Migrations table does not exist. Please create it manually using the SQL above.');
      } else if (queryError) {
        console.error('❌ Error checking migrations table:', queryError);
        throw queryError;
      }

      console.log('✅ Migrations table ready');
    } catch (error) {
      console.error('❌ Error ensuring migrations table:', error);
      throw error;
    }
  }

  /**
   * Get list of applied migrations
   */
  private async getAppliedMigrations(): Promise<string[]> {
    try {
      const { data, error } = await supabase
        .from('schema_migrations')
        .select('filename')
        .order('applied_at', { ascending: true });

      if (error) {
        console.error('❌ Error fetching applied migrations:', error);
        throw error;
      }

      return data.map(row => row.filename);
    } catch (error) {
      console.error('❌ Error getting applied migrations:', error);
      throw error;
    }
  }

  /**
   * Apply a single migration
   */
  private async applyMigration(migration: MigrationFile): Promise<void> {
    try {
      console.log(`📄 Applying migration: ${migration.filename}`);

      // Execute the migration SQL
      const { error: migrationError } = await supabase.rpc('exec_sql', {
        sql: migration.content
      });

      if (migrationError) {
        console.error(`❌ Error applying migration ${migration.filename}:`, migrationError);
        throw migrationError;
      }

      // Record the migration as applied
      const { error: recordError } = await supabase
        .from('schema_migrations')
        .insert({
          filename: migration.filename,
          checksum: this.calculateChecksum(migration.content)
        });

      if (recordError) {
        console.error(`❌ Error recording migration ${migration.filename}:`, recordError);
        throw recordError;
      }

      console.log(`✅ Applied migration: ${migration.filename}`);
    } catch (error) {
      console.error(`❌ Failed to apply migration ${migration.filename}:`, error);
      throw error;
    }
  }

  /**
   * Calculate checksum for migration content
   */
  private calculateChecksum(content: string): string {
    const crypto = require('crypto');
    return crypto.createHash('sha256').update(content).digest('hex').substring(0, 16);
  }

  /**
   * Run all pending migrations
   */
  async migrate(): Promise<void> {
    try {
      console.log('🚀 Starting database migration...');

      // Ensure migrations table exists
      await this.ensureMigrationsTable();

      // Get migration files and applied migrations
      const migrationFiles = this.getMigrationFiles();
      const appliedMigrations = await this.getAppliedMigrations();

      console.log(`📁 Found ${migrationFiles.length} migration files`);
      console.log(`✅ ${appliedMigrations.length} migrations already applied`);

      // Filter out already applied migrations
      const pendingMigrations = migrationFiles.filter(
        migration => !appliedMigrations.includes(migration.filename)
      );

      if (pendingMigrations.length === 0) {
        console.log('✅ No pending migrations to apply');
        return;
      }

      console.log(`📋 ${pendingMigrations.length} pending migrations to apply:`);
      pendingMigrations.forEach(migration => {
        console.log(`   - ${migration.filename}`);
      });

      // Apply each pending migration
      for (const migration of pendingMigrations) {
        await this.applyMigration(migration);
      }

      console.log('🎉 All migrations applied successfully!');
    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }
  }

  /**
   * Test database connection
   */
  async testConnection(): Promise<void> {
    try {
      const { data, error } = await supabase
        .from('conversations')
        .select('count')
        .limit(1);

      if (error) {
        console.error('❌ Database connection test failed:', error);
        throw error;
      }

      console.log('✅ Database connection successful');
    } catch (error) {
      console.error('❌ Database connection test failed:', error);
      throw error;
    }
  }

  /**
   * Rollback last migration (for development/testing)
   */
  async rollbackLast(): Promise<void> {
    try {
      console.log('⚠️  Rolling back last migration...');

      const { data, error } = await supabase
        .from('schema_migrations')
        .select('*')
        .order('applied_at', { ascending: false })
        .limit(1);

      if (error) {
        console.error('❌ Error fetching last migration:', error);
        throw error;
      }

      if (!data || data.length === 0) {
        console.log('ℹ️  No migrations to rollback');
        return;
      }

      const lastMigration = data[0];
      console.log(`🔄 Rolling back: ${lastMigration.filename}`);

      // Note: This is a simple implementation that just removes the migration record
      // In a production system, you'd want to have proper rollback scripts
      const { error: deleteError } = await supabase
        .from('schema_migrations')
        .delete()
        .eq('filename', lastMigration.filename);

      if (deleteError) {
        console.error('❌ Error rolling back migration:', deleteError);
        throw deleteError;
      }

      console.log(`✅ Rolled back: ${lastMigration.filename}`);
      console.log('⚠️  Note: This only removes the migration record. Manual cleanup may be required.');
    } catch (error) {
      console.error('❌ Rollback failed:', error);
      throw error;
    }
  }
}

// Main execution
async function main() {
  const migrator = new DatabaseMigrator();
  const command = process.argv[2];

  try {
    // Test database connection first
    await migrator.testConnection();

    switch (command) {
      case 'migrate':
      case undefined:
        await migrator.migrate();
        break;
      case 'rollback':
        await migrator.rollbackLast();
        break;
      case 'status':
        console.log('📊 Migration status check not implemented yet');
        break;
      default:
        console.log('Usage: npm run migrate [migrate|rollback|status]');
        process.exit(1);
    }
  } catch (error) {
    console.error('❌ Migration script failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

export { DatabaseMigrator };
